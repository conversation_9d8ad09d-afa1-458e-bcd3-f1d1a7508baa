package me.zivush.grimoireenchant.enchantments;

import io.lumine.mythic.api.MythicProvider;
import io.lumine.mythic.api.mobs.MythicMob;
import io.lumine.mythic.bukkit.BukkitAdapter;
import io.lumine.mythic.core.mobs.ActiveMob;
import me.zivush.grimoireenchant.GrimoireEnchant;
import me.zivush.grimoireenchant.GrimoireEnchantment;
import me.zivush.grimoireenchant.utils.ColorUtils;
import org.bukkit.Location;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerTeleportEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.persistence.PersistentDataType;
import org.bukkit.NamespacedKey;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Random;
import java.util.UUID;

/**
 * Summons enchantment implementation
 */
public class SummonsEnchantment extends GrimoireEnchantment implements Listener {

    // NBT key to mark summoned entities
    public static final String SUMMONED_NBT_KEY = "GrimoireSummoned";

    /**
     * Class to track a group of summons with their own timer
     */
    private class SummonGroup {
        private final List<ActiveMob> mobs;
        private final BukkitTask despawnTask;

        public SummonGroup(List<ActiveMob> mobs, BukkitTask despawnTask) {
            this.mobs = mobs;
            this.despawnTask = despawnTask;
        }

        public List<ActiveMob> getMobs() {
            return mobs;
        }

        public BukkitTask getDespawnTask() {
            return despawnTask;
        }

        public void cancelDespawnTask() {
            if (despawnTask != null && !despawnTask.isCancelled()) {
                despawnTask.cancel();
            }
        }

        public void despawn() {
            for (ActiveMob mob : mobs) {
                if (mob != null && mob.getEntity() != null && mob.getEntity().isValid()) {
                    mob.remove();
                }
            }
        }
    }

    // Map to track player summons with a composite key of player UUID and enchantment ID
    private static final Map<String, List<SummonGroup>> playerSummons = new HashMap<>();

    // Helper method to create a unique key for a player and enchantment ID
    private String getPlayerKey(UUID playerUUID) {
        return playerUUID.toString() + ":" + id;
    }
    private final Random random = new Random();

    /**
     * Mark an entity as summoned by adding NBT data
     *
     * @param entity The entity to mark as summoned
     */
    public static void markAsSummoned(Entity entity) {
        if (entity != null) {
            NamespacedKey key = new NamespacedKey("grimoireenchant", SUMMONED_NBT_KEY.toLowerCase());
            entity.getPersistentDataContainer().set(key, PersistentDataType.BYTE, (byte) 1);
        }
    }

    /**
     * Check if an entity is summoned by checking NBT data
     *
     * @param entity The entity to check
     * @return True if the entity is summoned, false otherwise
     */
    public static boolean isSummoned(Entity entity) {
        if (entity == null) return false;
        NamespacedKey key = new NamespacedKey("grimoireenchant", SUMMONED_NBT_KEY.toLowerCase());
        return entity.getPersistentDataContainer().has(key, PersistentDataType.BYTE);
    }

    /**
     * Constructor for SummonsEnchantment
     *
     * @param plugin The main plugin instance
     * @param id The enchantment ID (e.g., "Summons", "Summons1", "Summons2")
     */
    public SummonsEnchantment(GrimoireEnchant plugin, String id) {
        super(plugin, id);

        // Register events
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }

    @Override
    public void activate(Player player, ItemStack item, String rarity) {
        // Check cooldown and mana
        if (!canUse(player, rarity)) {
            return;
        }

        // Get configuration for this rarity
        String configPath = "Abilities." + id + ".Rarity." + rarity;
        ConfigurationSection config = plugin.getConfig().getConfigurationSection(configPath);
        if (config == null) {
            // Debug message to help troubleshoot
            if (plugin.getConfig().getBoolean("Debug", false)) {
                plugin.getLogger().info("Could not find config section: " + configPath);
            }
            return;
        }

        String mobType = config.getString("Mob-Type", "Skeleton");

        // Debug message to verify we're using the correct configuration
        if (plugin.getConfig().getBoolean("Debug", false)) {
            plugin.getLogger().info("Using enchantment ID: " + id + ", Mob Type: " + mobType);
        }
        int mobAmount = config.getInt("Mob-Amount", 1);
        int duration = config.getInt("Mob-Duration", 30);
        int manaCost = config.getInt("Mana-Cost", 30);

        // Get spawn radius from main config
        double spawnRadius = plugin.getConfig().getDouble("Abilities." + id + ".Spawn-Radius", 3.0);

        // Check if the MythicMob type exists before consuming mana
        try {
            Optional<MythicMob> optMythicMob = MythicProvider.get().getMobManager().getMythicMob(mobType);
            if (!optMythicMob.isPresent()) {
                player.sendMessage(ColorUtils.process("&#FF6347MythicMob type '" + mobType + "' not found."));
                return;
            }
        } catch (Exception e) {
            player.sendMessage(ColorUtils.process("&#FF6347Error checking MythicMob type: " + e.getMessage()));
            return;
        }

        // Use mana
        if (!plugin.getEnchantmentManager().useMana(player, manaCost)) {
            return;
        }

        // Set cooldown
        setCooldown(player);

        // Play sound
        playTriggerSound(player);

        // Create a list to store the spawned mobs
        List<ActiveMob> spawnedMobs = new ArrayList<>();

        try {
            Optional<MythicMob> optMythicMob = MythicProvider.get().getMobManager().getMythicMob(mobType);

            if (!optMythicMob.isPresent()) {
                player.sendMessage(ColorUtils.process("&#FF6347MythicMob type '" + mobType + "' not found."));
                return;
            }

            MythicMob mythicMobToSpawn = optMythicMob.get();

            // Spawn the requested number of mobs
            for (int i = 0; i < mobAmount; i++) {
                // Calculate a random position within the spawn radius
                Location spawnLocation = getRandomLocationAround(player.getLocation(), spawnRadius);

                // Spawn the MythicMob (level 1 is standard)
                ActiveMob spawnedMob = mythicMobToSpawn.spawn(BukkitAdapter.adapt(spawnLocation), 1.0);

                if (spawnedMob == null || spawnedMob.getEntity() == null || !spawnedMob.getEntity().isValid()) {
                    player.sendMessage(ColorUtils.process("&#FF6347Failed to spawn a MythicMob."));
                    continue;
                }

                // Set the player as the owner of the mob
                spawnedMob.setOwner(player.getUniqueId());

                // Mark the entity as summoned using NBT
                Entity bukkitEntity = spawnedMob.getEntity().getBukkitEntity();
                markAsSummoned(bukkitEntity);

                // Add to our list of spawned mobs
                spawnedMobs.add(spawnedMob);
            }

            // If we couldn't spawn any mobs, return
            if (spawnedMobs.isEmpty()) {
                return;
            }

            // Get the player's UUID
            UUID playerUUID = player.getUniqueId();

            // Create a composite key for this player and enchantment
            String playerKey = getPlayerKey(playerUUID);

            // Get or create the player's summon groups list
            List<SummonGroup> summonGroups = playerSummons.computeIfAbsent(playerKey, k -> new ArrayList<>());

            // Count total existing summons
            int existingSummonCount = 0;
            for (SummonGroup group : summonGroups) {
                existingSummonCount += group.getMobs().size();
            }

            // Create a despawn task for this group of summons
            BukkitTask despawnTask = new BukkitRunnable() {
                @Override
                public void run() {
                    if (player.isOnline()) {
                        // Send message about the specific batch of summons expiring
                        String despawnMsg;
                        if (spawnedMobs.size() == 1) {
                            despawnMsg = plugin.getMessagesConfig().getString(id + ".DespawnSingle",
                                plugin.getMessagesConfig().getString("Summons.DespawnSingle",
                                "&#4CBB17One of your companions has returned to the spirit realm."));
                        } else {
                            despawnMsg = plugin.getMessagesConfig().getString(id + ".DespawnMultiple",
                                plugin.getMessagesConfig().getString("Summons.DespawnMultiple",
                                "&#4CBB17&#FFCC33{count} &#4CBB17of your companions have returned to the spirit realm."))
                                .replace("{count}", String.valueOf(spawnedMobs.size()));
                        }

                        // Create a composite key for this player and enchantment
                        String playerKey = getPlayerKey(playerUUID);

                        // Remove this group from the player's summon groups
                        if (playerSummons.containsKey(playerKey)) {
                            List<SummonGroup> groups = playerSummons.get(playerKey);

                            // Find and remove this group
                            Iterator<SummonGroup> iterator = groups.iterator();
                            while (iterator.hasNext()) {
                                SummonGroup group = iterator.next();
                                if (group.getMobs() == spawnedMobs) {
                                    // Despawn the mobs in this group
                                    group.despawn();
                                    iterator.remove();
                                    break;
                                }
                            }

                            // Count remaining summons
                            int remainingSummons = 0;
                            for (SummonGroup group : groups) {
                                remainingSummons += group.getMobs().size();
                            }

                            // Add remaining count if there are still summons left
                            if (remainingSummons > 0) {
                                despawnMsg += plugin.getMessagesConfig().getString(id + ".RemainingCount",
                                    plugin.getMessagesConfig().getString("Summons.RemainingCount",
                                    " (&#FFCC33{count} &#4CBB17remaining)"))
                                    .replace("{count}", String.valueOf(remainingSummons));
                            }

                            // If no groups left, remove the player from the map
                            if (groups.isEmpty()) {
                                playerSummons.remove(playerKey);
                            }
                        }

                        player.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "") + despawnMsg));
                    } else {
                        // Player is offline, just clean up
                        String playerKey = getPlayerKey(playerUUID);
                        if (playerSummons.containsKey(playerKey)) {
                            List<SummonGroup> groups = playerSummons.get(playerKey);

                            // Find and remove this group
                            Iterator<SummonGroup> iterator = groups.iterator();
                            while (iterator.hasNext()) {
                                SummonGroup group = iterator.next();
                                if (group.getMobs() == spawnedMobs) {
                                    group.despawn();
                                    iterator.remove();
                                    break;
                                }
                            }

                            // If no groups left, remove the player from the map
                            if (groups.isEmpty()) {
                                playerSummons.remove(playerKey);
                            }
                        }
                    }
                }
            }.runTaskLater(plugin, duration * 20L);

            // Create a new summon group and add it to the player's list
            SummonGroup newGroup = new SummonGroup(spawnedMobs, despawnTask);
            summonGroups.add(newGroup);

            // Send message
            String summonedMsg;
            if (existingSummonCount > 0) {
                // Message for additional summons
                summonedMsg = plugin.getMessagesConfig().getString(id + ".Spawn.Additional",
                    plugin.getMessagesConfig().getString("Summons.Spawn.Additional",
                    "&#4CBB17You have summoned &#FFCC33{count} &#4CBB17more companions! (&#FFCC33{total} &#4CBB17total)"))
                    .replace("{count}", String.valueOf(spawnedMobs.size()))
                    .replace("{total}", String.valueOf(existingSummonCount + spawnedMobs.size()));
            } else if (spawnedMobs.size() == 1) {
                summonedMsg = plugin.getMessagesConfig().getString(id + ".Spawn.Single",
                    plugin.getMessagesConfig().getString("Summons.Spawn.Single",
                    "&#4CBB17You have summoned a companion to fight by your side!"));
            } else {
                summonedMsg = plugin.getMessagesConfig().getString(id + ".Spawn.Multiple",
                    plugin.getMessagesConfig().getString("Summons.Spawn.Multiple",
                    "&#4CBB17You have summoned &#FFCC33{count} &#4CBB17companions to fight by your side!"))
                    .replace("{count}", String.valueOf(spawnedMobs.size()));
            }
            player.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "") + summonedMsg));

        } catch (Exception e) {
            player.sendMessage(ColorUtils.process("&#FF6347An error occurred while summoning your companion."));
            plugin.getLogger().severe("Error during MythicMob spawn: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Despawn all mobs for a player for this specific enchantment
     *
     * @param player The player
     */
    private void despawnAllMobs(Player player) {
        UUID playerUUID = player.getUniqueId();
        String playerKey = getPlayerKey(playerUUID);

        if (playerSummons.containsKey(playerKey)) {
            List<SummonGroup> groups = playerSummons.get(playerKey);
            for (SummonGroup group : groups) {
                // Cancel the despawn task
                group.cancelDespawnTask();
                // Despawn the mobs
                group.despawn();
            }
            playerSummons.remove(playerKey);
        }
    }

    /**
     * Get a random location around a center point
     *
     * @param center The center location
     * @param radius The radius to search within
     * @return A random location within the radius
     */
    private Location getRandomLocationAround(Location center, double radius) {
        // Generate a random angle and distance within the radius
        double angle = random.nextDouble() * 2 * Math.PI;
        double distance = random.nextDouble() * radius;

        // Calculate the offset
        double x = distance * Math.cos(angle);
        double z = distance * Math.sin(angle);

        // Create a new location with the same Y coordinate as the player
        Location location = center.clone().add(x, 0, z);

        // Find a safe spawning location near the player's height
        location = findSafeSpawnLocation(location, center.getY());

        return location;
    }

    /**
     * Find a safe spawning location near the target Y level
     *
     * @param location The base location (X, Z coordinates)
     * @param targetY The target Y coordinate (player's Y level)
     * @return A safe location to spawn the mob
     */
    private Location findSafeSpawnLocation(Location location, double targetY) {
        // Search range above and below the target Y level
        int searchRange = 5;

        // Start from the target Y level and search downward first, then upward
        for (int offset = 0; offset <= searchRange; offset++) {
            // Check below target Y first
            if (offset > 0) {
                double checkY = targetY - offset;
                if (checkY >= 0 && isSafeSpawnLocation(location, checkY)) {
                    location.setY(checkY);
                    return location;
                }
            }

            // Check above target Y
            double checkY = targetY + offset;
            if (checkY <= location.getWorld().getMaxHeight() && isSafeSpawnLocation(location, checkY)) {
                location.setY(checkY);
                return location;
            }
        }

        // If no safe location found within range, fall back to a location near the player
        // Try to find solid ground below the player's level
        for (int y = (int) targetY; y >= Math.max(0, targetY - 10); y--) {
            if (isSafeSpawnLocation(location, y)) {
                location.setY(y);
                return location;
            }
        }

        // Last resort: use the player's exact Y level
        location.setY(targetY);
        return location;
    }

    /**
     * Check if a location is safe for spawning a mob
     *
     * @param location The location to check
     * @param y The Y coordinate to check
     * @return True if the location is safe for spawning
     */
    private boolean isSafeSpawnLocation(Location location, double y) {
        Location checkLoc = location.clone();
        checkLoc.setY(y);

        // Check if there's solid ground below (or at) the spawn point
        Location groundCheck = checkLoc.clone();
        groundCheck.setY(y - 1);

        // Check if there's enough space above for the mob (2 blocks high)
        Location spaceCheck1 = checkLoc.clone();
        Location spaceCheck2 = checkLoc.clone();
        spaceCheck2.setY(y + 1);

        // The spawn location should have air or passable blocks
        // The location below should be solid (unless we're checking the exact ground level)
        // The location above should have space
        boolean hasSpace = (spaceCheck1.getBlock().isPassable() || spaceCheck1.getBlock().getType().isAir()) &&
                          (spaceCheck2.getBlock().isPassable() || spaceCheck2.getBlock().getType().isAir());

        boolean hasGround = !groundCheck.getBlock().isPassable() || y <= 1; // Allow spawning at bedrock level

        return hasSpace && hasGround;
    }

    /**
     * Handle player teleport event to despawn mobs
     */
    @EventHandler
    public void onPlayerTeleport(PlayerTeleportEvent event) {
        Player player = event.getPlayer();
        String playerKey = getPlayerKey(player.getUniqueId());

        // Only despawn mobs if player teleports to a different world
        if (playerSummons.containsKey(playerKey) &&
            !event.getFrom().getWorld().equals(event.getTo().getWorld())) {

            // Send message about despawning
            String despawnMsg = plugin.getMessagesConfig().getString(id + ".DespawnTeleport",
                plugin.getMessagesConfig().getString("Summons.DespawnTeleport",
                "&#FF6347Your companions cannot follow you to another world."));
            player.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "") + despawnMsg));

            // Despawn the mobs
            despawnAllMobs(player);
        }
    }

    /**
     * Handle player death event to despawn all summons
     */
    @EventHandler
    public void onPlayerDeath(org.bukkit.event.entity.PlayerDeathEvent event) {
        Player player = event.getEntity();
        String playerKey = getPlayerKey(player.getUniqueId());

        // Check if player has any summons
        if (playerSummons.containsKey(playerKey)) {
            // Send message about despawning
            String despawnMsg = plugin.getMessagesConfig().getString(id + ".DespawnDeath",
                plugin.getMessagesConfig().getString("Summons.DespawnDeath",
                "&#FF6347Your companions have returned to the spirit realm upon your death."));

            // Schedule the message to be sent after respawn to ensure player can see it
            plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
                if (player.isOnline()) {
                    player.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "") + despawnMsg));
                }
            }, 20L); // 1 second delay

            // Despawn the mobs immediately
            despawnAllMobs(player);
        }
    }
}
